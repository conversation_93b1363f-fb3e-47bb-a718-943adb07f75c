# Khắc <PERSON>c Vấn Đề Di Chuyển Nhân Vật

## Vấn Đề Thường Gặp

### 1. <PERSON><PERSON><PERSON> Vật Không Thể Di Chu<PERSON>ển

**Triệu chứng:**
- <PERSON>h<PERSON>n WASD nhưng nhân vật đứng yên
- Không có phản ứng với input

**Nguyên nhân và cách khắc phục:**

#### A. <PERSON><PERSON><PERSON> tra CharacterController
```
Trong Inspector của Player:
✅ Character Controller component phải có:
   - Height: 2
   - Radius: 0.5  
   - Center: (0, 1, 0)
   - Skin Width: 0.08
```

#### B. Kiểm tra Input System
```
Trong PlayerInput component:
✅ Actions: Phải gán InputSystem_Actions asset
✅ Behavior: Đặt thành "Send Messages"
✅ Default Map: "Player"
```

#### C. Kiểm tra Ground Check
```
Tạo Empty GameObject con tên "GroundCheck":
✅ Position: (0, -1, 0) relative to player
✅ <PERSON><PERSON> là con của Player object
```

### 2. <PERSON><PERSON><PERSON> Vật Không Thể Nhảy

**Triệu chứng:**
- Nhấn Space nhưng không nhảy
- Nhảy rất thấp hoặc không rời mặt đất

**Cách khắc phục:**

#### A. Kiểm tra Jump Settings
```
Trong PlayerMovement component:
✅ Jump Height: 2-3 (tùy ý muốn)
✅ Gravity: -9.81 hoặc -15 (mạnh hơn)
✅ Jump Timeout: 0.1
```

#### B. Kiểm tra Ground Detection
```
Trong PlayerController:
✅ Ground Distance: 0.3
✅ Ground Mask: Chọn layer của mặt đất
✅ Ground Check object phải ở đúng vị trí
```

### 3. Nhân Vật Không Thể Chạy Nhanh

**Triệu chứng:**
- Giữ Shift nhưng tốc độ không tăng
- Sprint không hoạt động

**Cách khắc phục:**

#### A. Kiểm tra Sprint Input
```
Trong Input Actions:
✅ Sprint action phải được bind với Left Shift
✅ Action Type: "Button" 
✅ Interactions: "Hold"
```

#### B. Kiểm tra Sprint Settings
```
Trong PlayerMovement:
✅ Walk Speed: 5
✅ Sprint Speed: 8-10 (phải lớn hơn Walk Speed)
✅ Acceleration: 15 (tăng để responsive hơn)
```

### 4. Di Chuyển Không Mượt Mà

**Triệu chứng:**
- Nhân vật giật cục khi di chuyển
- Tăng/giảm tốc quá chậm

**Cách khắc phục:**

#### A. Tăng Acceleration/Deceleration
```
Trong PlayerMovement component:
✅ Acceleration: 15-20
✅ Deceleration: 15-20
```

#### B. Kiểm tra Frame Rate
```
Trong Project Settings > Time:
✅ Fixed Timestep: 0.02 (50Hz)
✅ Maximum Allowed Timestep: 0.1
```

## Công Cụ Hỗ Trợ Debug

### 1. Sử dụng PlayerMovementDebugger

```csharp
// Thêm component này vào Player để debug
1. Chọn Player object
2. Add Component > PlayerMovementDebugger
3. Chạy game và xem thông tin debug trên màn hình
```

### 2. Sử dụng PlayerMovementFixer

```csharp
// Tự động sửa các vấn đề thường gặp
1. Menu: Tools > Player Controller > Fix Movement Issues
2. Hoặc thêm PlayerMovementFixer component và nhấn "Fix Player Movement Issues"
```

### 3. Kiểm tra Setup

```csharp
// Kiểm tra xem setup có đúng không
Menu: Tools > Player Controller > Validate Setup
```

## Thiết Lập Từng Bước

### Bước 1: Tạo Player Object
```
1. Tạo Empty GameObject, đặt tên "Player"
2. Add Component: CharacterController
3. Add Component: PlayerInput
4. Add Component: PlayerController
5. Add Component: PlayerMovement
```

### Bước 2: Cấu Hình CharacterController
```
Height: 2
Radius: 0.5
Center: (0, 1, 0)
Skin Width: 0.08
```

### Bước 3: Tạo GroundCheck
```
1. Tạo Empty GameObject con tên "GroundCheck"
2. Position: (0, -1, 0)
3. Kéo vào Ground Check field trong PlayerController
```

### Bước 4: Cấu Hình Input
```
1. Gán InputSystem_Actions vào PlayerInput
2. Đặt Behavior thành "Send Messages"
3. Default Map: "Player"
```

### Bước 5: Thiết Lập Camera
```
1. Tạo Camera con của Player
2. Add Component: PlayerCamera
3. Gán vào Player Camera field trong PlayerController
```

### Bước 6: Cấu Hình Layer
```
1. Tạo layer "Ground" cho mặt đất
2. Đặt Ground Mask trong PlayerController thành "Ground"
3. Đặt mặt đất vào layer "Ground"
```

## Kiểm Tra Nhanh

### Checklist Debug
```
□ CharacterController có đúng settings không?
□ GroundCheck ở đúng vị trí không?
□ Input Actions đã được gán chưa?
□ Ground layer mask đã đúng chưa?
□ Camera reference đã được gán chưa?
□ Movement settings có hợp lý không?
```

### Test Input
```csharp
// Thêm vào Update() để test input
void Update()
{
    Vector2 input = GetComponent<PlayerInput>().actions["Move"].ReadValue<Vector2>();
    if (input != Vector2.zero)
        Debug.Log("Input detected: " + input);
}
```

### Test Ground Detection
```csharp
// Thêm vào Update() để test ground check
void Update()
{
    bool grounded = GetComponent<PlayerController>().IsGrounded;
    Debug.Log("Is Grounded: " + grounded);
}
```

## Lỗi Thường Gặp và Cách Sửa

### Lỗi 1: "NullReferenceException" khi di chuyển
```
Nguyên nhân: Thiếu reference đến component
Cách sửa: Kiểm tra tất cả references trong Inspector
```

### Lỗi 2: Nhân vật "bay" hoặc rơi xuyên đất
```
Nguyên nhân: Ground detection không hoạt động
Cách sửa: Kiểm tra Ground Layer Mask và GroundCheck position
```

### Lỗi 3: Input không phản hồi
```
Nguyên nhân: Input Actions chưa được gán hoặc sai behavior
Cách sửa: Gán InputSystem_Actions và đặt behavior thành "Send Messages"
```

### Lỗi 4: Di chuyển theo hướng sai
```
Nguyên nhân: Camera direction không được tính đúng
Cách sửa: Đảm bảo Camera.main tồn tại và PlayerCamera được gán đúng
```

## Tối Ưu Hiệu Suất

### 1. Giảm Raycast
```csharp
// Thay vì raycast mỗi frame, dùng timer
private float groundCheckTimer = 0f;
private const float GROUND_CHECK_INTERVAL = 0.1f;

void Update()
{
    groundCheckTimer += Time.deltaTime;
    if (groundCheckTimer >= GROUND_CHECK_INTERVAL)
    {
        CheckGrounded();
        groundCheckTimer = 0f;
    }
}
```

### 2. Sử dụng FixedUpdate cho Physics
```csharp
// Di chuyển physics code vào FixedUpdate
void FixedUpdate()
{
    // Movement và gravity calculations
}
```

Với hướng dẫn này, bạn sẽ có thể khắc phục hầu hết các vấn đề về di chuyển nhân vật!
