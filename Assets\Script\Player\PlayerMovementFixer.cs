using UnityEngine;
using UnityEngine.InputSystem;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class PlayerMovementFixer : MonoBehaviour
{
    [Header("Auto Fix Settings")]
    [SerializeField] private bool autoFixOnStart = true;
    [SerializeField] private bool showFixResults = true;
    
    void Start()
    {
        if (autoFixOnStart)
        {
            FixPlayerMovementIssues();
        }
    }
    
    [ContextMenu("Fix Player Movement Issues")]
    public void FixPlayerMovementIssues()
    {
        PlayerController playerController = GetComponent<PlayerController>();
        if (playerController == null)
        {
            Debug.LogError("PlayerMovementFixer: No PlayerController found!");
            return;
        }
        
        int fixesApplied = 0;
        
        // Fix 1: Character Controller Settings
        fixesApplied += FixCharacterController();
        
        // Fix 2: Input System Setup
        fixesApplied += FixInputSystem();
        
        // Fix 3: Ground Check Setup
        fixesApplied += FixGroundCheck();
        
        // Fix 4: Camera Reference
        fixesApplied += FixCameraReference();
        
        // Fix 5: Movement Settings
        fixesApplied += FixMovementSettings();
        
        if (showFixResults)
        {
            Debug.Log($"PlayerMovementFixer: Applied {fixesApplied} fixes to {gameObject.name}");
        }
    }
    
    int FixCharacterController()
    {
        CharacterController cc = GetComponent<CharacterController>();
        if (cc == null)
        {
            cc = gameObject.AddComponent<CharacterController>();
            Debug.Log("Added missing CharacterController");
            return 1;
        }
        
        int fixes = 0;
        
        // Fix height
        if (cc.height < 1.5f || cc.height > 2.5f)
        {
            cc.height = 2f;
            fixes++;
        }
        
        // Fix radius
        if (cc.radius < 0.3f || cc.radius > 0.7f)
        {
            cc.radius = 0.5f;
            fixes++;
        }
        
        // Fix center
        if (Vector3.Distance(cc.center, new Vector3(0, 1, 0)) > 0.1f)
        {
            cc.center = new Vector3(0, 1, 0);
            fixes++;
        }
        
        // Fix skin width
        if (cc.skinWidth < 0.05f || cc.skinWidth > 0.15f)
        {
            cc.skinWidth = 0.08f;
            fixes++;
        }
        
        if (fixes > 0 && showFixResults)
        {
            Debug.Log($"Fixed {fixes} CharacterController settings");
        }
        
        return fixes;
    }
    
    int FixInputSystem()
    {
        PlayerInput playerInput = GetComponent<PlayerInput>();
        if (playerInput == null)
        {
            playerInput = gameObject.AddComponent<PlayerInput>();
            Debug.Log("Added missing PlayerInput component");
            return 1;
        }
        
        int fixes = 0;
        
        // Check if input actions are assigned
        if (playerInput.actions == null)
        {
            // Try to find the input actions asset
            string[] guids = UnityEditor.AssetDatabase.FindAssets("InputSystem_Actions t:InputActionAsset");
            if (guids.Length > 0)
            {
                string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
                InputActionAsset inputActions = UnityEditor.AssetDatabase.LoadAssetAtPath<InputActionAsset>(path);
                playerInput.actions = inputActions;
                fixes++;
                Debug.Log("Assigned InputSystem_Actions to PlayerInput");
            }
        }
        
        // Set behavior to Send Messages
        if (playerInput.notificationBehavior != PlayerNotifications.SendMessages)
        {
            playerInput.notificationBehavior = PlayerNotifications.SendMessages;
            fixes++;
            Debug.Log("Set PlayerInput behavior to Send Messages");
        }
        
        return fixes;
    }
    
    int FixGroundCheck()
    {
        Transform groundCheck = transform.Find("GroundCheck");
        if (groundCheck == null)
        {
            GameObject groundCheckObj = new GameObject("GroundCheck");
            groundCheckObj.transform.SetParent(transform);
            groundCheckObj.transform.localPosition = new Vector3(0, -1, 0);
            Debug.Log("Created GroundCheck object");
            return 1;
        }
        
        // Check position
        if (Vector3.Distance(groundCheck.localPosition, new Vector3(0, -1, 0)) > 0.1f)
        {
            groundCheck.localPosition = new Vector3(0, -1, 0);
            Debug.Log("Fixed GroundCheck position");
            return 1;
        }
        
        return 0;
    }
    
    int FixCameraReference()
    {
        PlayerController playerController = GetComponent<PlayerController>();
        if (playerController.PlayerCamera == null)
        {
            Camera mainCamera = Camera.main;
            if (mainCamera != null)
            {
                PlayerCamera playerCamera = mainCamera.GetComponent<PlayerCamera>();
                if (playerCamera != null)
                {
                    // Use reflection to set the camera reference
                    var field = typeof(PlayerController).GetField("playerCamera", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (field != null)
                    {
                        field.SetValue(playerController, playerCamera);
                        Debug.Log("Fixed camera reference");
                        return 1;
                    }
                }
            }
        }
        
        return 0;
    }
    
    int FixMovementSettings()
    {
        PlayerMovement playerMovement = GetComponent<PlayerMovement>();
        if (playerMovement == null) return 0;
        
        int fixes = 0;
        
        // Check if movement settings are reasonable
        PlayerController playerController = GetComponent<PlayerController>();
        
        // Sync settings from PlayerController to PlayerMovement
        if (playerController != null)
        {
            playerMovement.SetMovementSettings(
                playerController.WalkSpeed,
                playerController.SprintSpeed,
                playerController.CrouchSpeed,
                15f, // Better acceleration
                15f  // Better deceleration
            );
            fixes++;
        }
        
        if (fixes > 0 && showFixResults)
        {
            Debug.Log("Updated movement settings for better responsiveness");
        }
        
        return fixes;
    }
    
    // Validation method to check if everything is set up correctly
    public bool ValidateSetup()
    {
        bool isValid = true;
        
        // Check required components
        if (GetComponent<CharacterController>() == null)
        {
            Debug.LogError("Missing CharacterController component");
            isValid = false;
        }
        
        if (GetComponent<PlayerInput>() == null)
        {
            Debug.LogError("Missing PlayerInput component");
            isValid = false;
        }
        
        if (GetComponent<PlayerMovement>() == null)
        {
            Debug.LogError("Missing PlayerMovement component");
            isValid = false;
        }
        
        // Check ground check
        if (transform.Find("GroundCheck") == null)
        {
            Debug.LogError("Missing GroundCheck object");
            isValid = false;
        }
        
        // Check input actions
        PlayerInput playerInput = GetComponent<PlayerInput>();
        if (playerInput != null && playerInput.actions == null)
        {
            Debug.LogError("PlayerInput actions not assigned");
            isValid = false;
        }
        
        return isValid;
    }
    
#if UNITY_EDITOR
    [MenuItem("Tools/Player Controller/Fix Movement Issues")]
    static void FixMovementIssuesMenuItem()
    {
        PlayerController player = FindFirstObjectByType<PlayerController>();
        if (player != null)
        {
            PlayerMovementFixer fixer = player.GetComponent<PlayerMovementFixer>();
            if (fixer == null)
            {
                fixer = player.gameObject.AddComponent<PlayerMovementFixer>();
            }
            
            fixer.FixPlayerMovementIssues();
        }
        else
        {
            Debug.LogWarning("No PlayerController found in scene");
        }
    }
    
    [MenuItem("Tools/Player Controller/Validate Setup")]
    static void ValidateSetupMenuItem()
    {
        PlayerController player = FindFirstObjectByType<PlayerController>();
        if (player != null)
        {
            PlayerMovementFixer fixer = player.GetComponent<PlayerMovementFixer>();
            if (fixer == null)
            {
                fixer = player.gameObject.AddComponent<PlayerMovementFixer>();
            }
            
            bool isValid = fixer.ValidateSetup();
            if (isValid)
            {
                Debug.Log("✅ Player setup is valid!");
            }
            else
            {
                Debug.Log("❌ Player setup has issues. Use 'Fix Movement Issues' to resolve them.");
            }
        }
        else
        {
            Debug.LogWarning("No PlayerController found in scene");
        }
    }
#endif
}
