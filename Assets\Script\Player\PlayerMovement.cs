using UnityEngine;

public class PlayerMovement : MonoBehaviour
{
    [Header("Movement Settings")]
    [SerializeField] private float walkSpeed = 5f;
    [SerializeField] private float sprintSpeed = 8f;
    [SerializeField] private float crouchSpeed = 2f;
    [SerializeField] private float acceleration = 15f;
    [SerializeField] private float deceleration = 15f;
    
    [Header("Jump Settings")]
    [SerializeField] private float jumpHeight = 2f;
    [SerializeField] private float gravity = -9.81f;
    [SerializeField] private float jumpTimeout = 0.1f;
    [SerializeField] private float fallTimeout = 0.15f;
    
    [Header("Crouch Settings")]
    [SerializeField] private float crouchHeight = 1f;
    [SerializeField] private float standingHeight = 2f;
    [SerializeField] private float crouchTransitionSpeed = 10f;
    
    [Header("Air Control")]
    [SerializeField] private float airControl = 0.3f;
    
    // Private variables
    private CharacterController characterController;
    private PlayerController playerController;
    
    private Vector3 velocity;
    private Vector3 currentMovement;
    private float currentSpeed;
    private float targetSpeed;
    
    // Jump variables
    private float jumpTimeoutDelta;
    private float fallTimeoutDelta;
    
    // Crouch variables
    private bool isCrouching;
    private float targetHeight;
    private float currentHeight;
    
    // State properties
    public bool IsMoving => currentMovement.magnitude > 0.1f;
    public bool IsSprinting { get; private set; }
    public bool IsCrouching => isCrouching;
    public bool IsJumping => !playerController.IsGrounded && velocity.y > 0;
    public bool IsFalling => !playerController.IsGrounded && velocity.y < 0;
    
    private void Awake()
    {
        characterController = GetComponent<CharacterController>();
        playerController = GetComponent<PlayerController>();
        
        // Initialize height values
        currentHeight = characterController.height;
        targetHeight = currentHeight;
        standingHeight = currentHeight;
    }
    
    private void Start()
    {
        // Reset timeouts
        jumpTimeoutDelta = jumpTimeout;
        fallTimeoutDelta = fallTimeout;
    }
    
    public void UpdateMovement(Vector2 moveInput, bool jumpInput, bool sprintInput, bool crouchInput, bool isGrounded)
    {
        HandleCrouch(crouchInput);
        HandleMovement(moveInput, sprintInput, isGrounded);
        HandleJump(jumpInput, isGrounded);
        HandleGravity(isGrounded);
        
        // Apply movement
        characterController.Move(currentMovement * Time.deltaTime);
    }
    
    private void HandleMovement(Vector2 moveInput, bool sprintInput, bool isGrounded)
    {
        // Determine target speed based on input and state
        if (moveInput == Vector2.zero)
        {
            targetSpeed = 0f;
        }
        else if (isCrouching)
        {
            targetSpeed = crouchSpeed;
            IsSprinting = false;
        }
        else if (sprintInput && isGrounded)
        {
            targetSpeed = sprintSpeed;
            IsSprinting = true;
        }
        else
        {
            targetSpeed = walkSpeed;
            IsSprinting = false;
        }
        
        // Smooth speed transition
        float speedChangeRate = (currentSpeed < targetSpeed) ? acceleration : deceleration;
        currentSpeed = Mathf.Lerp(currentSpeed, targetSpeed, speedChangeRate * Time.deltaTime);
        
        // Calculate movement direction
        Vector3 inputDirection = new Vector3(moveInput.x, 0f, moveInput.y).normalized;
        
        if (inputDirection != Vector3.zero)
        {
            // Get camera forward and right vectors for proper movement direction
            Transform cameraTransform = Camera.main != null ? Camera.main.transform : transform;
            Vector3 cameraForward = cameraTransform.forward;
            Vector3 cameraRight = cameraTransform.right;

            // Remove Y component and normalize
            cameraForward.y = 0f;
            cameraRight.y = 0f;
            cameraForward.Normalize();
            cameraRight.Normalize();

            // Calculate world direction based on camera orientation
            Vector3 worldDirection = (cameraForward * inputDirection.z + cameraRight * inputDirection.x).normalized;

            // Apply movement with air control consideration
            float controlMultiplier = isGrounded ? 1f : airControl;
            Vector3 targetMovement = worldDirection * currentSpeed * controlMultiplier;

            // More responsive movement transition
            float lerpSpeed = acceleration * Time.deltaTime;
            currentMovement.x = Mathf.Lerp(currentMovement.x, targetMovement.x, lerpSpeed);
            currentMovement.z = Mathf.Lerp(currentMovement.z, targetMovement.z, lerpSpeed);
        }
        else
        {
            // More responsive deceleration when no input
            float decelerationSpeed = deceleration * Time.deltaTime;
            currentMovement.x = Mathf.Lerp(currentMovement.x, 0f, decelerationSpeed);
            currentMovement.z = Mathf.Lerp(currentMovement.z, 0f, decelerationSpeed);
        }
        
        // Apply vertical velocity
        currentMovement.y = velocity.y;
    }
    
    private void HandleJump(bool jumpInput, bool isGrounded)
    {
        if (isGrounded)
        {
            // Reset fall timeout
            fallTimeoutDelta = fallTimeout;

            // Stop falling
            if (velocity.y < 0f)
            {
                velocity.y = -2f; // Small negative value to keep grounded
            }

            // Jump - improved logic
            if (jumpInput && jumpTimeoutDelta <= 0f && !isCrouching)
            {
                // Calculate jump velocity for desired height
                float jumpVelocity = Mathf.Sqrt(jumpHeight * -2f * gravity);
                velocity.y = jumpVelocity;

                // Reset jump timeout to prevent multiple jumps
                jumpTimeoutDelta = jumpTimeout;

                Debug.Log($"Jumping with velocity: {jumpVelocity}");
            }

            // Jump timeout countdown
            if (jumpTimeoutDelta > 0f)
            {
                jumpTimeoutDelta -= Time.deltaTime;
            }
        }
        else
        {
            // Reset jump timeout when in air
            jumpTimeoutDelta = jumpTimeout;

            // Fall timeout countdown
            if (fallTimeoutDelta > 0f)
            {
                fallTimeoutDelta -= Time.deltaTime;
            }
        }
    }
    
    private void HandleGravity(bool isGrounded)
    {
        if (isGrounded && velocity.y < 0f)
        {
            velocity.y = -2f; // Small negative value to keep grounded
        }
        else
        {
            velocity.y += gravity * Time.deltaTime;
        }
    }
    
    private void HandleCrouch(bool crouchInput)
    {
        if (crouchInput && !isCrouching)
        {
            // Start crouching
            isCrouching = true;
            targetHeight = crouchHeight;
        }
        else if (!crouchInput && isCrouching)
        {
            // Check if we can stand up
            if (CanStandUp())
            {
                isCrouching = false;
                targetHeight = standingHeight;
            }
        }
        
        // Smooth height transition
        if (Mathf.Abs(currentHeight - targetHeight) > 0.01f)
        {
            currentHeight = Mathf.Lerp(currentHeight, targetHeight, crouchTransitionSpeed * Time.deltaTime);
            characterController.height = currentHeight;
            
            // Adjust center to keep feet on ground
            Vector3 center = characterController.center;
            center.y = currentHeight / 2f;
            characterController.center = center;
        }
    }
    
    private bool CanStandUp()
    {
        // Check if there's enough space above to stand up
        Vector3 capsuleBottom = transform.position + characterController.center - Vector3.up * (currentHeight / 2f);
        Vector3 capsuleTop = capsuleBottom + Vector3.up * standingHeight;
        
        return !Physics.CheckCapsule(capsuleBottom, capsuleTop, characterController.radius * 0.9f, 
            LayerMask.GetMask("Default"), QueryTriggerInteraction.Ignore);
    }
    
    // Public methods for other components
    public void AddForce(Vector3 force)
    {
        velocity += force;
    }
    
    public void SetVelocity(Vector3 newVelocity)
    {
        velocity = newVelocity;
    }
    
    public Vector3 GetVelocity()
    {
        return new Vector3(currentMovement.x, velocity.y, currentMovement.z);
    }
    
    public float GetCurrentSpeed()
    {
        return currentSpeed;
    }
    
    public float GetMaxSpeed()
    {
        if (isCrouching) return crouchSpeed;
        if (IsSprinting) return sprintSpeed;
        return walkSpeed;
    }

    // Setter methods for PlayerController
    public void SetMovementSettings(float walk, float sprint, float crouch, float accel, float decel)
    {
        walkSpeed = walk;
        sprintSpeed = sprint;
        crouchSpeed = crouch;
        acceleration = accel;
        deceleration = decel;
    }

    public void SetJumpSettings(float height, float grav)
    {
        jumpHeight = height;
        gravity = grav;
    }

    public void SetAirControl(float control)
    {
        airControl = control;
    }

    public void SetCrouchSettings(float crouchH, float standingH, float transitionSpeed)
    {
        crouchHeight = crouchH;
        standingHeight = standingH;
        crouchTransitionSpeed = transitionSpeed;
    }
}
