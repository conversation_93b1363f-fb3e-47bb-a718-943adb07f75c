# Player Controller System

This is a comprehensive player controller system for Unity that uses the new Input System. The system is modular and extensible, allowing for easy customization and addition of new features.

## Components Overview

### Core Components

1. **PlayerController.cs** - Main controller that coordinates all player systems
2. **PlayerMovement.cs** - Handles character movement, jumping, sprinting, and crouching
3. **PlayerCamera.cs** - Manages camera control and mouse look
4. **PlayerInteraction.cs** - Handles interaction with objects in the world
5. **PlayerCombat.cs** - Manages attack and combat mechanics

### Supporting Files

6. **InputSystem_Actions.cs** - Generated wrapper for Unity's Input System
7. **ExampleInteractable.cs** - Example implementations of interactable and damageable objects
8. **PlayerSetupHelper.cs** - Editor utility for quick setup and scene creation
9. **PlayerControllerDemo.cs** - Demo script showing how to interact with the player system
10. **PlayerMovementDebugger.cs** - Debug tool for movement issues with real-time UI
11. **PlayerMovementFixer.cs** - Automatic fixer for common movement problems

## Setup Instructions

### Quick Setup (Recommended)

1. **Using the Setup Helper:**
   - Go to `GameObject > 3D Object > Player Controller` in the Unity menu
   - This will automatically create a fully configured player with camera and ground check
   - Use `Tools > Player Controller > Create Example Scene` to add test objects

### Manual Setup

1. Create an empty GameObject in your scene and name it "Player"
2. Add the following components to the Player GameObject:
   - CharacterController
   - PlayerInput (Unity's component)
   - PlayerController (our script)

3. The PlayerController will automatically add the other required components:
   - PlayerMovement
   - PlayerCamera
   - PlayerInteraction
   - PlayerCombat

### 2. Input System Setup

1. Make sure the InputSystem_Actions.inputactions file is properly configured
2. In the PlayerInput component, set the Actions to reference the InputSystem_Actions asset
3. Set the Behavior to "Send Messages" or "Invoke Unity Events"

### 3. Camera Setup

1. Create a Camera GameObject as a child of the Player
2. Position it at head level (usually around Y = 1.6)
3. Assign this camera to the PlayerController's "Player Camera Component" field
4. The system supports both first-person and third-person modes

### 4. Ground Check Setup

1. The system automatically creates a ground check point
2. You can manually assign a Transform to the "Ground Check" field for custom positioning
3. Adjust the "Ground Distance" and "Ground Mask" as needed for your terrain

## Input Mapping

The system uses the following input actions:

- **Move** (Vector2) - WASD/Arrow keys, Left stick
- **Look** (Vector2) - Mouse delta, Right stick
- **Jump** (Button) - Space, A button (gamepad)
- **Sprint** (Button) - Left Shift, Left stick press
- **Crouch** (Button) - C, B button (gamepad)
- **Attack** (Button) - Left Mouse, X button (gamepad)
- **Interact** (Button) - E, Y button (gamepad)
- **ToggleCamera** (Button) - V, Right stick press

## Features

### Movement System
- Smooth acceleration and deceleration
- Variable speed (walk, sprint, crouch)
- Air control for better feel
- Smooth crouching with collision detection
- Coyote time for jumping

### Camera System
- Mouse and gamepad support
- Adjustable sensitivity
- Smooth camera movement option
- Camera shake effects
- First-person and third-person modes
- Automatic cursor locking

### Interaction System
- Raycast or sphere-cast detection
- Visual feedback for interactables
- Continuous and instant interactions
- Progress tracking for long interactions
- Customizable interaction prompts

### Combat System
- Melee attack system
- Damage dealing with force application
- Attack cooldowns and timing
- Hit effects (camera shake, hit stop)
- Damage interface for extensibility

## Customization

### Adding New Interactions

1. Create a class that inherits from `InteractableBase`
2. Override the necessary methods:
   ```csharp
   public override void Interact(PlayerInteraction playerInteraction)
   {
       // Your interaction logic here
   }
   ```

### Adding New Damage Types

1. Implement the `IDamageable` interface on your objects
2. Handle damage in the `TakeDamage` method:
   ```csharp
   public void TakeDamage(DamageInfo damageInfo)
   {
       // Your damage handling logic here
   }
   ```

### Modifying Movement

1. Adjust values in the PlayerMovement component
2. For custom movement behaviors, override methods in PlayerMovement
3. Add new movement states by extending the existing system

## Performance Considerations

- The system uses object pooling for temporary effects
- Raycasts are optimized with layer masks
- Input is processed efficiently through the new Input System
- Components are modular to allow disabling unused features

## Troubleshooting

### Common Issues

1. **Player not moving**: Check that the CharacterController is properly configured and the ground layer mask is correct
2. **Camera not working**: Ensure the camera reference is assigned and the Input System is properly set up
3. **Interactions not working**: Verify the interaction layer mask and range settings
4. **Input not responding**: Check that the Input Actions asset is assigned and enabled

### Debug Features

- Ground check visualization in Scene view
- Interaction range gizmos
- Attack range visualization
- Console logging for interactions and damage
- **PlayerMovementDebugger** - Real-time movement debug UI
- **PlayerMovementFixer** - Automatic problem detection and fixing
- Menu tools: Tools > Player Controller > Fix Movement Issues

## Extension Ideas

- Add inventory system integration
- Implement weapon switching
- Add animation system integration
- Create UI for health/stamina bars
- Add sound effects integration
- Implement save/load functionality

## Dependencies

- Unity 2022.3 LTS or newer
- Input System package
- Universal Render Pipeline (optional, for better visuals)

## License

This player controller system is provided as-is for educational and development purposes.
